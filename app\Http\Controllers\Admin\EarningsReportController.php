<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Earning;
use App\Models\User;
use App\Services\EarningsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class EarningsReportController extends Controller
{
    protected $earningsService;

    public function __construct(EarningsService $earningsService)
    {
        $this->earningsService = $earningsService;
    }

    /**
     * Display the earnings report
     */
    public function index()
    {
        $earnings = Earning::with(['remittance', 'sendingMerchant', 'receivingMerchant'])
                          ->orderBy('created_at', 'desc')
                          ->paginate(config('basic.paginate'));

        $totalEarnings = Earning::sum('net_earnings');
        $merchants = User::where('merchant', 1)->select('id', 'firstname', 'lastname', 'username')->get();
        $paymentGateways = Earning::distinct()->pluck('payment_gateway')->filter();

        $data = [
            'page_title' => 'Earnings Report',
            'earnings' => $earnings,
            'totalEarnings' => $totalEarnings,
            'merchants' => $merchants,
            'paymentGateways' => $paymentGateways,
        ];

        return view('admin.earnings.index', $data);
    }

    /**
     * Search and filter earnings
     */
    public function search(Request $request)
    {
        $query = Earning::with(['remittance', 'sendingMerchant', 'receivingMerchant']);

        // Date range filter
        if ($request->filled('start_date')) {
            $query->where('created_at', '>=', Carbon::parse($request->start_date)->startOfDay());
        }

        if ($request->filled('end_date')) {
            $query->where('created_at', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        // Merchant filter
        if ($request->filled('merchant_id')) {
            $query->where(function($q) use ($request) {
                $q->where('sending_merchant_id', $request->merchant_id)
                  ->orWhere('receiving_merchant_id', $request->merchant_id);
            });
        }

        // Payment gateway filter
        if ($request->filled('payment_gateway')) {
            $query->where('payment_gateway', $request->payment_gateway);
        }

        // Currency filter
        if ($request->filled('currency')) {
            $query->where(function($q) use ($request) {
                $q->where('send_currency', $request->currency)
                  ->orWhere('receive_currency', $request->currency);
            });
        }

        $earnings = $query->orderBy('created_at', 'desc')
                         ->paginate(config('basic.paginate'));

        // Calculate filtered total earnings
        $filteredTotalEarnings = $query->sum('net_earnings');

        $merchants = User::where('merchant', 1)->select('id', 'firstname', 'lastname', 'username')->get();
        $paymentGateways = Earning::distinct()->pluck('payment_gateway')->filter();

        $data = [
            'page_title' => 'Earnings Report',
            'earnings' => $earnings,
            'totalEarnings' => $filteredTotalEarnings,
            'merchants' => $merchants,
            'paymentGateways' => $paymentGateways,
        ];

        return view('admin.earnings.index', $data);
    }

    /**
     * Export earnings to CSV
     */
    public function exportCsv(Request $request)
    {
        $query = Earning::with(['remittance', 'sendingMerchant', 'receivingMerchant']);

        // Apply same filters as search
        if ($request->filled('start_date')) {
            $query->where('created_at', '>=', Carbon::parse($request->start_date)->startOfDay());
        }

        if ($request->filled('end_date')) {
            $query->where('created_at', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        if ($request->filled('merchant_id')) {
            $query->where(function($q) use ($request) {
                $q->where('sending_merchant_id', $request->merchant_id)
                  ->orWhere('receiving_merchant_id', $request->merchant_id);
            });
        }

        if ($request->filled('payment_gateway')) {
            $query->where('payment_gateway', $request->payment_gateway);
        }

        if ($request->filled('currency')) {
            $query->where(function($q) use ($request) {
                $q->where('send_currency', $request->currency)
                  ->orWhere('receive_currency', $request->currency);
            });
        }

        $earnings = $query->orderBy('created_at', 'desc')->get();
        $totalEarnings = $earnings->sum('net_earnings');

        $filename = 'earnings_report_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($earnings, $totalEarnings) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Date',
                'Remittance ID',
                'Sending Merchant',
                'Receiving Merchant',
                'Total Fees',
                'Sending Merchant Share',
                'Receiving Merchant Share',
                'Net Earnings',
                'Payment Gateway',
                'Send Currency',
                'Receive Currency'
            ]);

            // Data rows
            foreach ($earnings as $earning) {
                fputcsv($file, [
                    $earning->created_at->format('Y-m-d H:i:s'),
                    $earning->remittance ? $earning->remittance->invoice : 'N/A',
                    $earning->sendingMerchant ? $earning->sendingMerchant->username : 'N/A',
                    $earning->receivingMerchant ? $earning->receivingMerchant->username : 'N/A',
                    $earning->formatted_total_fees,
                    $earning->formatted_sending_merchant_share,
                    $earning->formatted_receiving_merchant_share,
                    $earning->formatted_net_earnings,
                    $earning->payment_gateway ?? 'N/A',
                    $earning->send_currency ?? 'N/A',
                    $earning->receive_currency ?? 'N/A'
                ]);
            }

            // Total row
            fputcsv($file, []);
            fputcsv($file, [
                'TOTAL EARNINGS:',
                '',
                '',
                '',
                '',
                '',
                '',
                getAmount($totalEarnings, config('basic.fraction_number')),
                '',
                '',
                ''
            ]);

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * Export earnings to PDF
     */
    public function exportPdf(Request $request)
    {
        $query = Earning::with(['remittance', 'sendingMerchant', 'receivingMerchant']);

        // Apply same filters as search
        if ($request->filled('start_date')) {
            $query->where('created_at', '>=', Carbon::parse($request->start_date)->startOfDay());
        }

        if ($request->filled('end_date')) {
            $query->where('created_at', '<=', Carbon::parse($request->end_date)->endOfDay());
        }

        if ($request->filled('merchant_id')) {
            $query->where(function($q) use ($request) {
                $q->where('sending_merchant_id', $request->merchant_id)
                  ->orWhere('receiving_merchant_id', $request->merchant_id);
            });
        }

        if ($request->filled('payment_gateway')) {
            $query->where('payment_gateway', $request->payment_gateway);
        }

        if ($request->filled('currency')) {
            $query->where(function($q) use ($request) {
                $q->where('send_currency', $request->currency)
                  ->orWhere('receive_currency', $request->currency);
            });
        }

        $earnings = $query->orderBy('created_at', 'desc')->get();
        $totalEarnings = $earnings->sum('net_earnings');

        $data = [
            'earnings' => $earnings,
            'totalEarnings' => $totalEarnings,
            'filters' => $request->all(),
            'generatedAt' => now()->format('Y-m-d H:i:s'),
        ];

        $pdf = Pdf::loadView('admin.earnings.pdf', $data);

        $filename = 'earnings_report_' . date('Y-m-d_H-i-s') . '.pdf';

        return $pdf->download($filename);
    }
}
