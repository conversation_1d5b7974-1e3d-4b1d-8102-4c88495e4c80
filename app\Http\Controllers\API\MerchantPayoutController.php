<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\SendMoney;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Traits\Notify;
use Facades\App\Services\BasicService;
use Illuminate\Support\Facades\Log;

class MerchantPayoutController extends Controller
{
    use Notify;

    public function payout(Request $request)
    {
        $validator = validator()->make($request->all(), [
            'invoice_number' => 'required|numeric|min:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $user = auth()->user();

		if ($user->merchant == 0) {
			return response()->json([
				'status' => false,
				'message' => 'Unauthorized merchant access'
			], 403);
		}

		if ($user->status == 0) {
			return response()->json([
				'status' => false,
				'message' => 'Merchant is not active!'
			], 403);
		}

		// Check if merchant has a country assigned
		if (!$user->country_id) {
			return response()->json([
				'status' => false,
				'message' => 'You need to have a country assigned to your merchant account. Please contact support.'
			], 403);
		}

        $sendMoney = SendMoney::where('invoice', $request->invoice_number)
                             ->where('payment_status', 1)
                             ->first();

        if (!$sendMoney) {
            return response()->json([
                'status' => false,
                'message' => 'Invalid Invoice Number'
            ], 404);
        }

        if ($sendMoney->user_id == $user->id) {
            return response()->json([
                'status' => false,
                'message' => 'You cannot payout this transaction'
            ], 403);
        }

        if ($sendMoney->status == '1') {
            return response()->json([
                'status' => false,
                'message' => 'This Transaction is already Closed'
            ], 400);
        }

        // Check if the receive_currency_id matches the merchant's country_id
        if ($sendMoney->receive_currency_id != $user->country_id) {
            return response()->json([
                'status' => false,
                'message' => 'You can only process payouts for transactions to your assigned country'
            ], 403);
        }

        if ($sendMoney->status == '2') {
            $merchantCom = 0;
            if (0 < $sendMoney->fees) {
                $basicCom = config('basic.merchant_profit');
                $baseCharge = $sendMoney->fees / $sendMoney->send_curr_rate;
                $merchantCom = ($baseCharge * $basicCom) / 100;
                $sendMoney->admin_commission = $baseCharge - ($sendMoney->merchant_commission + $merchantCom);
                $sendMoney->merchant_profit = $merchantCom;
            }

            $sendMoney->received_at = Carbon::now();
            $sendMoney->status = 1;
            $sendMoney->merchant_id = $user->id;
            $sendMoney->save();

			//this code results wrong Balance Increase for Receiving Merchant, since his balance should only increased by send amount converted to base currency
            //$user->balance += $sendMoney->totalBaseAmountPay;
            //d$user->save();
			$payoutAmount = $sendMoney->send_amount / $sendMoney->send_curr_rate; // Remittance amount only
			$user->balance += $payoutAmount; // Credit only the remittance amount
			$user->save();

            $trx_id = strRandom();
            $remarks = "Your account has been credited for payout #".$sendMoney->invoice;
            BasicService::makeTransaction($user, getAmount($payoutAmount),  0, '+', $trx_id, $remarks );



            $user->balance +=  $merchantCom;
            $user->save();


            $remarks2 = "Your account has been credited profit for payout #".$sendMoney->invoice;
            BasicService::makeTransaction($user, getAmount($merchantCom),  0, '+', $trx_id, $remarks2 );


            // Send notifications
            $this->sendMailSms($user, 'PAYOUT_COMPLETE', [
                'amount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
                'currency' => $sendMoney->send_curr,
                'invoice' => $sendMoney->invoice
            ]);

            $msg = [
                'amount' => $sendMoney->totalPay,
                'currency' => $sendMoney->send_curr
            ];
            $action = [
                "link" => '#',
                "icon" => "fas fa-money-bill-alt text-white"
            ];

            $this->userPushNotification($user, 'PAYOUT_COMPLETE', $msg, $action);

            return response()->json([
                'status' => true,
                'message' => 'Payout completed successfully',
                'data' => [
                    'invoice' => $sendMoney->invoice,
                    'amount' => $sendMoney->totalPay,
                    'currency' => $sendMoney->send_curr,
                    'received_at' => $sendMoney->received_at
                ]
            ], 200);
        }

        return response()->json([
            'status' => false,
            'message' => 'Invalid transaction status'
        ], 400);
    }

	public function payoutInfo(Request $request)
	{
		//Log::info($request->query());
		$validator = validator()->make($request->query(), [
			'invoice_number' => 'required|numeric|min:10',
		]);

		if ($validator->fails()) {
			return response()->json([
				'status' => false,
				'message' => $validator->errors()->first()
			], 422);
		}

		$user = auth()->user();
		if ($user->merchant == 0) {
			return response()->json([
				'status' => false,
				'message' => 'Unauthorized merchant access'
			], 403);
		}

		if ($user->status == 0) {
			return response()->json([
				'status' => false,
				'message' => 'Merchant is not active!'
			], 403);
		}

		// Check if merchant has a country assigned
		if (!$user->country_id) {
			return response()->json([
				'status' => false,
				'message' => 'You need to have a country assigned to your merchant account. Please contact support.'
			], 403);
		}

		$sendMoney = SendMoney::where('invoice', $request->query('invoice_number'))
							 ->where('payment_status', 1)
							 ->first();

		if (!$sendMoney) {
			return response()->json([
				'status' => false,
				'message' => 'Invalid Invoice Number'
			], 404);
		}

		if ($sendMoney->user_id == $user->id) {
			return response()->json([
				'status' => false,
				'message' => 'You cannot payout this transaction'
			], 403);
		}

		if ($sendMoney->status == '1') {
			return response()->json([
				'status' => false,
				'message' => 'This Transaction is already Closed'
			], 400);
		}

		// Check if the receive_currency_id matches the merchant's country_id
		if ($sendMoney->receive_currency_id != $user->country_id) {
			return response()->json([
				'status' => false,
				'message' => 'You can only process payouts for transactions to your assigned country'
			], 403);
		}

		if ($sendMoney->status != '2') {
			return response()->json([
				'status' => false,
				'message' => 'Invalid transaction status'
			], 400);
		}

		// Get transaction status
		$status = $this->getTransactionStatus($sendMoney);

		// Prepare response data
		$data = [
			'transaction' => $sendMoney->invoice,
			'status' => $status,
			'transaction_date' => $sendMoney->paid_at ? $sendMoney->paid_at : $sendMoney->created_at,
			'service' => optional($sendMoney->service)->name,
			'service_provider' => optional($sendMoney->provider)->name,
			'send_amount' => [
				'amount' => getAmount($sendMoney->send_amount, config('basic.fraction_number')),
				'currency' => $sendMoney->send_curr
			],
			'fees' => [
				'amount' => getAmount($sendMoney->fees, config('basic.fraction_number')),
				'currency' => $sendMoney->send_curr
			],
			'discount' => [
				'has_discount' => (bool)$sendMoney->discount,
				'amount' => getAmount($sendMoney->discount, config('basic.fraction_number')),
				'currency' => $sendMoney->send_curr
			],
			'total_send_amount' => [
				'amount' => getAmount($sendMoney->totalPay, config('basic.fraction_number')),
				'currency' => $sendMoney->send_curr
			],
			'recipient_amount' => [
				'amount' => getAmount($sendMoney->recipient_get_amount, config('basic.fraction_number')),
				'currency' => $sendMoney->receive_curr
			],
			'rate' => sprintf('1 %s = %s %s',
				$sendMoney->send_curr,
				getAmount($sendMoney->rate, config('basic.fraction_number')),
				$sendMoney->receive_curr
			),
			'sender' => [
				'name' => $sendMoney->sender_name,
				'phone' => $sendMoney->sender_phone,
				'address' => $sendMoney->sender_address
			],
			'recipient' => [
				'name' => $sendMoney->recipient_name,
				'email' => $sendMoney->recipient_email,
				'phone' => $sendMoney->recipient_contact_no
			],
			'funding_source' => $sendMoney->fund_source,
			'sending_purpose' => $sendMoney->purpose
		];

		return response()->json([
			'status' => true,
			'data' => $data
		], 200);
	}

	private function getTransactionStatus($sendMoney)
	{
		if ($sendMoney->status == 0 && $sendMoney->payment_status == 0) {
			return 'Information Need';
		} elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 0) {
			return 'Sender Not Pay Yet';
		} elseif ($sendMoney->status == 3 || $sendMoney->payment_status == 2) {
			return 'Cancelled';
		} elseif ($sendMoney->status == 1 && $sendMoney->payment_status == 1) {
			return 'Completed';
		} elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 1) {
			return 'Processing';
		} elseif ($sendMoney->status == 2 && $sendMoney->payment_status == 3) {
			return 'Payment Hold';
		}
		return 'Unknown';
	}
}