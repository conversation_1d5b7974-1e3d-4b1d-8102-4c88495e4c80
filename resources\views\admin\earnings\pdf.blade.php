<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Earnings Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .filters {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .filters h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        .filter-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        .filter-label {
            font-weight: bold;
            color: #555;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 10px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #333;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .total-row {
            background-color: #e8f5e8;
            font-weight: bold;
        }
        .total-row td {
            border-top: 2px solid #28a745;
        }
        .summary {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .summary h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .summary-item {
            margin-bottom: 5px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 10px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Earnings Report</h1>
        <p>{{ config('basic.site_title', 'BeRemit') }}</p>
        <p>Generated on: {{ $generatedAt }}</p>
    </div>

    @if(!empty($filters))
    <div class="filters">
        <h3>Applied Filters:</h3>
        @if(!empty($filters['start_date']))
            <div class="filter-item">
                <span class="filter-label">Start Date:</span> {{ $filters['start_date'] }}
            </div>
        @endif
        @if(!empty($filters['end_date']))
            <div class="filter-item">
                <span class="filter-label">End Date:</span> {{ $filters['end_date'] }}
            </div>
        @endif
        @if(!empty($filters['merchant_id']))
            <div class="filter-item">
                <span class="filter-label">Merchant ID:</span> {{ $filters['merchant_id'] }}
            </div>
        @endif
        @if(!empty($filters['payment_gateway']))
            <div class="filter-item">
                <span class="filter-label">Payment Gateway:</span> {{ $filters['payment_gateway'] }}
            </div>
        @endif
        @if(!empty($filters['currency']))
            <div class="filter-item">
                <span class="filter-label">Currency:</span> {{ $filters['currency'] }}
            </div>
        @endif
    </div>
    @endif

    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Remittance ID</th>
                <th>Sending Merchant</th>
                <th>Receiving Merchant</th>
                <th>Total Fees</th>
                <th>Sending Share</th>
                <th>Receiving Share</th>
                <th>Net Earnings</th>
                <th>Gateway</th>
                <th>Currencies</th>
            </tr>
        </thead>
        <tbody>
            @forelse($earnings as $earning)
                <tr>
                    <td>{{ $earning->created_at->format('Y-m-d H:i') }}</td>
                    <td>
                        @if($earning->remittance)
                            #{{ $earning->remittance->invoice }}
                        @else
                            N/A
                        @endif
                    </td>
                    <td>
                        @if($earning->sendingMerchant)
                            {{ $earning->sendingMerchant->firstname }} {{ $earning->sendingMerchant->lastname }}
                            ({{ $earning->sendingMerchant->username }})
                        @else
                            Direct User
                        @endif
                    </td>
                    <td>
                        @if($earning->receivingMerchant)
                            {{ $earning->receivingMerchant->firstname }} {{ $earning->receivingMerchant->lastname }}
                            ({{ $earning->receivingMerchant->username }})
                        @else
                            N/A
                        @endif
                    </td>
                    <td class="text-right">{{ config('basic.currency_symbol') }}{{ $earning->formatted_total_fees }}</td>
                    <td class="text-right">{{ config('basic.currency_symbol') }}{{ $earning->formatted_sending_merchant_share }}</td>
                    <td class="text-right">{{ config('basic.currency_symbol') }}{{ $earning->formatted_receiving_merchant_share }}</td>
                    <td class="text-right">{{ config('basic.currency_symbol') }}{{ $earning->formatted_net_earnings }}</td>
                    <td>{{ $earning->payment_gateway ?? 'N/A' }}</td>
                    <td>{{ $earning->send_currency }} → {{ $earning->receive_currency }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center">No earnings data found</td>
                </tr>
            @endforelse
        </tbody>
        @if($earnings->count() > 0)
            <tfoot>
                <tr class="total-row">
                    <td colspan="7" class="text-right"><strong>Total Earnings:</strong></td>
                    <td class="text-right"><strong>{{ config('basic.currency_symbol') }}{{ getAmount($totalEarnings, config('basic.fraction_number')) }}</strong></td>
                    <td colspan="2"></td>
                </tr>
            </tfoot>
        @endif
    </table>

    <div class="summary">
        <h3>Summary</h3>
        <div class="summary-item">
            <strong>Total Records:</strong> {{ $earnings->count() }}
        </div>
        <div class="summary-item">
            <strong>Total Net Earnings:</strong> {{ config('basic.currency_symbol') }}{{ getAmount($totalEarnings, config('basic.fraction_number')) }}
        </div>
        <div class="summary-item">
            <strong>Report Period:</strong> 
            @if(!empty($filters['start_date']) && !empty($filters['end_date']))
                {{ $filters['start_date'] }} to {{ $filters['end_date'] }}
            @elseif(!empty($filters['start_date']))
                From {{ $filters['start_date'] }}
            @elseif(!empty($filters['end_date']))
                Until {{ $filters['end_date'] }}
            @else
                All Time
            @endif
        </div>
    </div>

    <div class="footer">
        <p>This report was generated automatically by {{ config('basic.site_title', 'BeRemit') }} on {{ $generatedAt }}</p>
        <p>For accounting purposes only. Please verify all figures before making financial decisions.</p>
    </div>
</body>
</html>
