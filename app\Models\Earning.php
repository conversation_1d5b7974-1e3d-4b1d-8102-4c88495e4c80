<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Earning extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'total_fees' => 'decimal:8',
        'sending_merchant_share' => 'decimal:8',
        'receiving_merchant_share' => 'decimal:8',
        'net_earnings' => 'decimal:8',
        'exchange_rate' => 'decimal:8',
    ];

    /**
     * Get the remittance that this earning belongs to
     */
    public function remittance()
    {
        return $this->belongsTo(SendMoney::class, 'remittance_id', 'id');
    }

    /**
     * Get the sending merchant
     */
    public function sendingMerchant()
    {
        return $this->belongsTo(User::class, 'sending_merchant_id', 'id');
    }

    /**
     * Get the receiving merchant
     */
    public function receivingMerchant()
    {
        return $this->belongsTo(User::class, 'receiving_merchant_id', 'id');
    }

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        if ($startDate && $endDate) {
            return $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif ($startDate) {
            return $query->where('created_at', '>=', $startDate);
        } elseif ($endDate) {
            return $query->where('created_at', '<=', $endDate);
        }
        
        return $query;
    }

    /**
     * Scope to filter by merchant
     */
    public function scopeByMerchant($query, $merchantId)
    {
        if ($merchantId) {
            return $query->where(function($q) use ($merchantId) {
                $q->where('sending_merchant_id', $merchantId)
                  ->orWhere('receiving_merchant_id', $merchantId);
            });
        }
        
        return $query;
    }

    /**
     * Scope to filter by payment gateway
     */
    public function scopeByGateway($query, $gateway)
    {
        if ($gateway) {
            return $query->where('payment_gateway', $gateway);
        }
        
        return $query;
    }

    /**
     * Get formatted total fees
     */
    public function getFormattedTotalFeesAttribute()
    {
        return getAmount($this->total_fees, config('basic.fraction_number'));
    }

    /**
     * Get formatted sending merchant share
     */
    public function getFormattedSendingMerchantShareAttribute()
    {
        return getAmount($this->sending_merchant_share, config('basic.fraction_number'));
    }

    /**
     * Get formatted receiving merchant share
     */
    public function getFormattedReceivingMerchantShareAttribute()
    {
        return getAmount($this->receiving_merchant_share, config('basic.fraction_number'));
    }

    /**
     * Get formatted net earnings
     */
    public function getFormattedNetEarningsAttribute()
    {
        return getAmount($this->net_earnings, config('basic.fraction_number'));
    }
}
