@extends('admin.layouts.app')
@section('title', trans($page_title))
@section('content')
    <div class="page-header card card-primary m-0 m-md-4 my-4 m-md-0 p-5 shadow">
        <form action="{{ route('admin.earnings-report.search') }}" method="get">
            <div class="row justify-content-between">
                <div class="col-md-6 col-lg-2">
                    <div class="form-group">
                        <label>@lang('Start Date')</label>
                        <input type="date" name="start_date" value="{{@request()->start_date}}" class="form-control">
                    </div>
                </div>
                <div class="col-md-6 col-lg-2">
                    <div class="form-group">
                        <label>@lang('End Date')</label>
                        <input type="date" name="end_date" value="{{@request()->end_date}}" class="form-control">
                    </div>
                </div>
                <div class="col-md-6 col-lg-2">
                    <div class="form-group">
                        <label>@lang('Merchant')</label>
                        <select name="merchant_id" class="form-control">
                            <option value="">@lang('All Merchants')</option>
                            @foreach($merchants as $merchant)
                                <option value="{{ $merchant->id }}" 
                                    @if(@request()->merchant_id == $merchant->id) selected @endif>
                                    {{ $merchant->firstname }} {{ $merchant->lastname }} ({{ $merchant->username }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-6 col-lg-2">
                    <div class="form-group">
                        <label>@lang('Payment Gateway')</label>
                        <select name="payment_gateway" class="form-control">
                            <option value="">@lang('All Gateways')</option>
                            @foreach($paymentGateways as $gateway)
                                <option value="{{ $gateway }}" 
                                    @if(@request()->payment_gateway == $gateway) selected @endif>
                                    {{ $gateway }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-6 col-lg-2">
                    <div class="form-group">
                        <label>@lang('Currency')</label>
                        <select name="currency" class="form-control">
                            <option value="">@lang('All Currencies')</option>
                            <option value="USD" @if(@request()->currency == 'USD') selected @endif>USD</option>
                            <option value="EUR" @if(@request()->currency == 'EUR') selected @endif>EUR</option>
                            <option value="GBP" @if(@request()->currency == 'GBP') selected @endif>GBP</option>
                            <option value="NGN" @if(@request()->currency == 'NGN') selected @endif>NGN</option>
                            <option value="GHS" @if(@request()->currency == 'GHS') selected @endif>GHS</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6 col-lg-2">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="submit" class="btn btn-block btn-primary">
                            <i class="fas fa-search"></i> @lang('Search')
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="card card-primary m-0 m-md-4 my-4 m-md-0 shadow">
        <div class="card-header">
            <h4 class="card-title">@lang('Earnings Report')</h4>
            <div class="card-header-action">
                <div class="btn-group">
                    <a href="{{ route('admin.earnings-report.export-csv', request()->all()) }}" 
                       class="btn btn-success btn-sm">
                        <i class="fas fa-file-csv"></i> @lang('Export CSV')
                    </a>
                    <a href="{{ route('admin.earnings-report.export-pdf', request()->all()) }}" 
                       class="btn btn-danger btn-sm">
                        <i class="fas fa-file-pdf"></i> @lang('Export PDF')
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="categories-show-table table table-hover table-striped table-bordered">
                    <thead class="thead-dark">
                    <tr>
                        <th scope="col">@lang('Date')</th>
                        <th scope="col">@lang('Remittance ID')</th>
                        <th scope="col">@lang('Sending Merchant')</th>
                        <th scope="col">@lang('Receiving Merchant')</th>
                        <th scope="col">@lang('Total Fees')</th>
                        <th scope="col">@lang('Sending Share')</th>
                        <th scope="col">@lang('Receiving Share')</th>
                        <th scope="col">@lang('Net Earnings')</th>
                        <th scope="col">@lang('Gateway')</th>
                        <th scope="col">@lang('Currencies')</th>
                    </tr>
                    </thead>
                    <tbody>
                    @forelse($earnings as $earning)
                        <tr>
                            <td data-label="@lang('Date')">
                                {{ $earning->created_at->format('Y-m-d H:i') }}
                            </td>
                            <td data-label="@lang('Remittance ID')">
                                @if($earning->remittance)
                                    <a href="{{ route('admin.money-transfer.details', $earning->remittance->id) }}" 
                                       target="_blank" class="text-primary">
                                        #{{ $earning->remittance->invoice }}
                                    </a>
                                @else
                                    N/A
                                @endif
                            </td>
                            <td data-label="@lang('Sending Merchant')">
                                @if($earning->sendingMerchant)
                                    <div class="d-flex align-items-center">
                                        <div class="mr-2">
                                            <img src="{{ getFile(config('location.user.path').$earning->sendingMerchant->image) }}" 
                                                 alt="user" class="rounded-circle" width="30" height="30">
                                        </div>
                                        <div>
                                            <div class="font-weight-medium">
                                                {{ $earning->sendingMerchant->firstname }} {{ $earning->sendingMerchant->lastname }}
                                            </div>
                                            <small class="text-muted">@{{ $earning->sendingMerchant->username }}</small>
                                        </div>
                                    </div>
                                @else
                                    <span class="text-muted">@lang('Direct User')</span>
                                @endif
                            </td>
                            <td data-label="@lang('Receiving Merchant')">
                                @if($earning->receivingMerchant)
                                    <div class="d-flex align-items-center">
                                        <div class="mr-2">
                                            <img src="{{ getFile(config('location.user.path').$earning->receivingMerchant->image) }}" 
                                                 alt="user" class="rounded-circle" width="30" height="30">
                                        </div>
                                        <div>
                                            <div class="font-weight-medium">
                                                {{ $earning->receivingMerchant->firstname }} {{ $earning->receivingMerchant->lastname }}
                                            </div>
                                            <small class="text-muted">@{{ $earning->receivingMerchant->username }}</small>
                                        </div>
                                    </div>
                                @else
                                    N/A
                                @endif
                            </td>
                            <td data-label="@lang('Total Fees')">
                                <span class="font-weight-bold text-info">
                                    {{ config('basic.currency_symbol') }}{{ $earning->formatted_total_fees }}
                                </span>
                            </td>
                            <td data-label="@lang('Sending Share')">
                                <span class="text-warning">
                                    {{ config('basic.currency_symbol') }}{{ $earning->formatted_sending_merchant_share }}
                                </span>
                            </td>
                            <td data-label="@lang('Receiving Share')">
                                <span class="text-warning">
                                    {{ config('basic.currency_symbol') }}{{ $earning->formatted_receiving_merchant_share }}
                                </span>
                            </td>
                            <td data-label="@lang('Net Earnings')">
                                <span class="font-weight-bold text-success">
                                    {{ config('basic.currency_symbol') }}{{ $earning->formatted_net_earnings }}
                                </span>
                            </td>
                            <td data-label="@lang('Gateway')">
                                <span class="badge badge-secondary">{{ $earning->payment_gateway ?? 'N/A' }}</span>
                            </td>
                            <td data-label="@lang('Currencies')">
                                <small>
                                    {{ $earning->send_currency }} → {{ $earning->receive_currency }}
                                </small>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="10" class="text-center text-muted py-4">
                                @lang('No earnings data found')
                            </td>
                        </tr>
                    @endforelse
                    </tbody>
                    @if($earnings->count() > 0)
                        <tfoot class="bg-light">
                            <tr>
                                <th colspan="7" class="text-right">@lang('Total Earnings:')</th>
                                <th class="text-success">
                                    {{ config('basic.currency_symbol') }}{{ getAmount($totalEarnings, config('basic.fraction_number')) }}
                                </th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot>
                    @endif
                </table>
                {{ $earnings->appends($_GET)->links('partials.pagination') }}
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        $(document).ready(function () {
            $('select').select2({
                selectOnClose: true
            });
        });
    </script>
@endpush
