<?php

namespace App\Services;

use App\Models\Earning;
use App\Models\SendMoney;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class EarningsService
{
    /**
     * Record earnings when a merchant pays out a remittance
     *
     * @param SendMoney $sendMoney
     * @param User $receivingMerchant
     * @return Earning|null
     */
    public function recordEarnings(SendMoney $sendMoney, User $receivingMerchant)
    {
        try {
            // Get the sending merchant (if any)
            $sendingMerchant = $sendMoney->user && $sendMoney->user->merchant ? $sendMoney->user : null;

            // Calculate earnings
            $totalFees = $this->calculateTotalFees($sendMoney);
            $sendingMerchantShare = $sendMoney->merchant_commission ?? 0;
            $receivingMerchantShare = $sendMoney->merchant_profit ?? 0;
            $netEarnings = $totalFees - $sendingMerchantShare - $receivingMerchantShare;

            // Determine payment gateway from the remittance
            $paymentGateway = $this->determinePaymentGateway($sendMoney);

            // Create earnings record
            $earning = Earning::create([
                'remittance_id' => $sendMoney->id,
                'sending_merchant_id' => $sendingMerchant ? $sendingMerchant->id : null,
                'receiving_merchant_id' => $receivingMerchant->id,
                'total_fees' => $totalFees,
                'sending_merchant_share' => $sendingMerchantShare,
                'receiving_merchant_share' => $receivingMerchantShare,
                'net_earnings' => $netEarnings,
                'payment_gateway' => $paymentGateway,
                'send_currency' => $sendMoney->send_curr,
                'receive_currency' => $sendMoney->receive_curr,
                'exchange_rate' => $sendMoney->rate,
            ]);

            Log::info('Earnings recorded', [
                'earning_id' => $earning->id,
                'remittance_id' => $sendMoney->id,
                'net_earnings' => $netEarnings
            ]);

            return $earning;

        } catch (\Exception $e) {
            Log::error('Failed to record earnings', [
                'remittance_id' => $sendMoney->id,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Calculate total fees from the remittance
     *
     * @param SendMoney $sendMoney
     * @return float
     */
    private function calculateTotalFees(SendMoney $sendMoney)
    {
        // Convert fees to base currency if needed
        $baseCharge = $sendMoney->fees / ($sendMoney->send_curr_rate ?? 1);
        return $baseCharge;
    }

    /**
     * Determine payment gateway from remittance
     *
     * @param SendMoney $sendMoney
     * @return string|null
     */
    private function determinePaymentGateway(SendMoney $sendMoney)
    {
        // Check if there's a payment record associated with this remittance
        $payment = $sendMoney->payment;
        
        if ($payment && $payment->gateway) {
            return $payment->gateway->name;
        }

        // Fallback to checking payment type or other indicators
        if (isset($sendMoney->payment_type)) {
            return $sendMoney->payment_type;
        }

        return 'Unknown';
    }

    /**
     * Get earnings summary for a date range
     *
     * @param string|null $startDate
     * @param string|null $endDate
     * @return array
     */
    public function getEarningsSummary($startDate = null, $endDate = null)
    {
        $query = Earning::query();

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        } elseif ($startDate) {
            $query->where('created_at', '>=', $startDate);
        } elseif ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        return [
            'total_earnings' => $query->sum('net_earnings'),
            'total_fees_collected' => $query->sum('total_fees'),
            'total_merchant_commissions' => $query->sum('sending_merchant_share') + $query->sum('receiving_merchant_share'),
            'transaction_count' => $query->count(),
        ];
    }

    /**
     * Get earnings by payment gateway
     *
     * @param string|null $startDate
     * @param string|null $endDate
     * @return \Illuminate\Support\Collection
     */
    public function getEarningsByGateway($startDate = null, $endDate = null)
    {
        $query = Earning::query();

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return $query->selectRaw('payment_gateway, SUM(net_earnings) as total_earnings, COUNT(*) as transaction_count')
                    ->groupBy('payment_gateway')
                    ->orderByDesc('total_earnings')
                    ->get();
    }

    /**
     * Get top earning merchants
     *
     * @param string|null $startDate
     * @param string|null $endDate
     * @param int $limit
     * @return \Illuminate\Support\Collection
     */
    public function getTopEarningMerchants($startDate = null, $endDate = null, $limit = 10)
    {
        $query = Earning::query();

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return $query->selectRaw('receiving_merchant_id, SUM(receiving_merchant_share) as total_commission, COUNT(*) as transaction_count')
                    ->whereNotNull('receiving_merchant_id')
                    ->with('receivingMerchant:id,firstname,lastname,username')
                    ->groupBy('receiving_merchant_id')
                    ->orderByDesc('total_commission')
                    ->limit($limit)
                    ->get();
    }
}
