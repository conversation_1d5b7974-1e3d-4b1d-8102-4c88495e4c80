<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEarningsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('earnings', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('remittance_id')->unsigned()->comment('Reference to send_money table');
            $table->bigInteger('sending_merchant_id')->unsigned()->nullable()->comment('Merchant who sent the remittance');
            $table->bigInteger('receiving_merchant_id')->unsigned()->nullable()->comment('Merchant who paid out the remittance');
            $table->decimal('total_fees', 18, 8)->default(0)->comment('Total fees collected from the transaction');
            $table->decimal('sending_merchant_share', 18, 8)->default(0)->comment('Commission paid to sending merchant');
            $table->decimal('receiving_merchant_share', 18, 8)->default(0)->comment('Commission paid to receiving merchant');
            $table->decimal('net_earnings', 18, 8)->default(0)->comment('Net earnings after deducting merchant shares');
            $table->string('payment_gateway')->nullable()->comment('Payment gateway used for the transaction');
            $table->string('send_currency', 10)->nullable()->comment('Sending currency code');
            $table->string('receive_currency', 10)->nullable()->comment('Receiving currency code');
            $table->decimal('exchange_rate', 18, 8)->nullable()->comment('Exchange rate used');
            $table->timestamps();

            // Add indexes for better performance
            $table->index('remittance_id');
            $table->index('sending_merchant_id');
            $table->index('receiving_merchant_id');
            $table->index('created_at');
            $table->index(['created_at', 'net_earnings']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('earnings');
    }
}
